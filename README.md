# SMS Expense Tracker

A Google Apps Script application that automatically processes SMS messages to track financial transactions and categorize them into expense sheets.

## 📁 File Structure

### `index.js` - Main Application
The core application file containing:
- **Configuration**: API URLs, spreadsheet settings, response schemas
- **Main Entry Point**: `doGet()` function for handling web requests
- **SMS Processing**: Gemini AI integration for SMS classification
- **Spreadsheet Operations**: Functions to log data to Google Sheets
- **Error Handling**: Comprehensive error management and logging

### `test-cases.js` - Testing Suite
Comprehensive testing functionality including:
- **Test Data**: 17+ test cases covering financial and non-financial scenarios
- **Test Functions**: Multiple testing approaches for validation
- **Utilities**: Helper functions for test data management

## 🚀 Core Features

### SMS Classification
The application uses Google's Gemini AI to classify SMS messages into two categories:

**Financial Transactions** (logged to monthly sheets):
- ✅ Money debited from your account
- ✅ Money credited to your account  
- ✅ Card purchases/payments
- ✅ UPI payments sent/received
- ✅ Bank transfers and ACH transactions

**Non-Financial Messages** (logged to "Others" sheet):
- ❌ OTPs and verification codes
- ❌ Balance inquiries/updates
- ❌ Payment confirmations (without actual debit/credit)
- ❌ Transaction declined/failed messages
- ❌ Payment reminders and due dates
- ❌ Money requests from others
- ❌ SIP/mutual fund transactions
- ❌ Promotional messages

### Data Logging
- **Financial transactions**: Logged to monthly sheets (Jan, Feb, Mar, etc.)
- **Non-financial messages**: Logged to "Others" sheet
- **Date handling**: Always uses current date for consistent logging
- **Data validation**: Comprehensive input validation and sanitization

## 🛠️ Setup Instructions

### 1. Google Apps Script Setup
1. Create a new Google Apps Script project
2. Copy the contents of `index.js` to the script editor
3. Set up the required script properties

### 2. Required Script Properties
In Google Apps Script, go to Project Settings > Script Properties and add:
- `GEMINI_API_KEY`: Your Google Gemini API key

### 3. Google Sheets Setup
1. Create a new Google Spreadsheet
2. Update the `SPREADSHEET_URL` in the CONFIG object in `index.js`
3. The application will automatically create sheets as needed

### 4. Deploy as Web App
1. In Google Apps Script, click "Deploy" > "New Deployment"
2. Choose "Web app" as the type
3. Set execute permissions appropriately
4. Copy the web app URL for SMS integration

## 🧪 Testing

### Running Tests
Copy the contents of `test-cases.js` to your Google Apps Script project and use:

```javascript
// Run all test cases
testScript()

// Test a single SMS message
testSMSClassification("Your SMS message here")

// Test specific category
testByCategory("financial")  // or "non-financial", "otp", etc.

// Clear test data (use with caution)
clearTestData()
```

### Test Coverage
The test suite includes:
- **Non-financial cases**: SIP transactions, OTPs, balance inquiries, payment confirmations, reminders, declined transactions, money requests, promotional messages
- **Financial cases**: UPI payments, bank transfers, card purchases, salary credits, ACH debits, bill payments, NEFT transfers

## 📊 Data Structure

### Financial Transaction Sheet Headers
| Summary | Account Name | Amount | Purpose | UPI ID | Type | Reference | Date | Time | Raw Message |

### Others Sheet Headers  
| Raw Message | Category | Summary | Date | Time |

## 🔧 Configuration

### Gemini Models
The application supports multiple Gemini models. Update the `geminiModels` array to use different models:
```javascript
const geminiModels = [
    "gemini-2.0-flash",
    "gemini-2.0-flash-001", 
    "gemini-2.5-flash-preview-05-20"
]
```

### Month Names
Customize month sheet names in the CONFIG object:
```javascript
MONTH_NAMES: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
```

## 🚨 Error Handling

The application includes comprehensive error handling:
- **API Retry Logic**: Exponential backoff for failed API calls
- **Input Validation**: Validates all inputs before processing
- **Detailed Logging**: Structured error logs with timestamps
- **Graceful Degradation**: Continues operation even with partial failures

## 📈 Performance Features

- **Retry Logic**: Automatic retry with exponential backoff for API failures
- **Input Validation**: Early validation to prevent unnecessary processing
- **Efficient Parsing**: Multiple JSON parsing strategies for reliability
- **Optimized Logging**: Batch operations where possible

## 🔒 Security Considerations

- **API Key Management**: Store API keys in Script Properties, not in code
- **Input Sanitization**: All inputs are validated and sanitized
- **Error Information**: Sensitive information is not exposed in error messages
- **Access Control**: Configure appropriate permissions for the web app

## 📝 Maintenance

### Adding New Test Cases
Add new test cases to the `TEST_MESSAGES` array in `test-cases.js`:
```javascript
{
    sms: "Your SMS message text",
    expected: "Financial (Description)" or "Non-financial (Description)",
    category: "finance" or specific category like "otp", "balance_inquiry", etc.
}
```

### Updating Classification Rules
Modify the prompt in the `getStructuredExpenseData()` function in `index.js` to adjust classification logic.

### Adding New Data Fields
1. Update the `RESPONSE_SCHEMA` object
2. Update the sheet headers in `CONFIG.HEADERS`
3. Update the data handling in `handleFinancialTransaction()` and `handleNonFinancialMessage()`

## 🤝 Contributing

When making changes:
1. Update test cases in `test-cases.js` to cover new scenarios
2. Run the full test suite to ensure no regressions
3. Update this README if adding new features or changing functionality
4. Follow the existing code structure and commenting patterns

## 📄 License

This project is provided as-is for educational and personal use.
