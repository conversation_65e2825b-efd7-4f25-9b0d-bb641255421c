const geminiModels = [
    "gemini-2.0-flash",
    "gemini-2.0-flash-001",
    "gemini-2.5-flash-preview-05-20"
]
const CONFIG = {
    SPREADSHEET_URL: 'https://docs.google.com/spreadsheets/d/1y-Kit9o2Rv9wlJq4AqJAXaYChMa7WxD52-MqNQrVxDw/edit',
    GEMINI_API_URL: `https://generativelanguage.googleapis.com/v1beta/models/${geminiModels[1]}:generateContent`,
    MONTH_NAMES: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
    HEADERS: {
        EXPENSE: ["Summary", "Account Name", "Amount", "Purpose", "UPI ID", "Type", "Reference", "Date", "Time", "Raw Message"],
        OTHERS: ["Raw Message", "Category", "Summary", "Date", "Time"]
    }
};

// Response schema for Gemini API
const RESPONSE_SCHEMA = {
    type: "object",
    properties: {
        account: {
            type: "string",
            description: "Bank account identifier (e.g., 'HDFC Bank A/c XX1234'). Empty string for non-financial messages."
        },
        name: {
            type: "string",
            description: "Name of person/entity in transaction. Empty string if not applicable."
        },
        error: {
            type: "string",
            description: "Error message if processing failed, empty string if successful"
        },
        amount: {
            type: "number",
            description: "Transaction amount as number. Use 0 for non-financial messages."
        },
        purpose: {
            type: "string",
            description: "Transaction purpose/description. Empty string for non-financial messages."
        },
        upiId: {
            type: "string",
            description: "UPI ID if applicable, empty string otherwise"
        },
        type: {
            type: "string",
            enum: ["debit", "credit", ""],
            description: "Transaction type: 'debit' for money going out, 'credit' for money coming in, empty string for non-financial"
        },
        isFinanceMsg: {
            type: "boolean",
            description: "true only for actual debit/credit transactions from/to user's own accounts, false for everything else"
        },
        reference: {
            type: "string",
            description: "Transaction reference number. Empty string for non-financial messages."
        },
        summary: {
            type: "string",
            description: "Brief 5-word summary of the message content"
        },
        category: {
            type: "string",
            description: "Message category: 'finance' for actual transactions, or specific category like 'otp', 'balance_inquiry', 'promotional', 'sip', 'payment_confirmation', etc."
        }
    },
    required: ["account", "name", "error", "amount", "purpose", "upiId", "type", "isFinanceMsg", "reference", "category", "summary"]
};

/**
 * Main entry point for the web app
 */
function doGet(e) {
    try {
        // Validate input parameters
        if (!e || !e.parameters) {
            return createJsonResponse({ success: false, error: 'No parameters provided' });
        }

        const rawSmsMsg = e.parameters.rawMsg;
        if (!rawSmsMsg) {
            return createJsonResponse({ success: false, error: 'No rawMsg parameter provided' });
        }

        // Handle array parameter (common in Google Apps Script)
        const smsMessage = Array.isArray(rawSmsMsg) ? rawSmsMsg[0] : rawSmsMsg.toString();

        if (!smsMessage || smsMessage.trim().length === 0) {
            return createJsonResponse({ success: false, error: 'Empty SMS message provided' });
        }

        console.log('Processing SMS message:', smsMessage.substring(0, 100) + '...');

        const expenseData = getStructuredExpenseData(smsMessage);
        if (!expenseData) {
            return createJsonResponse({ success: false, error: 'Failed to extract structured data from SMS' });
        }

        return createJsonResponse(processExpenseData(expenseData, smsMessage));
    } catch (error) {
        return handleError('Error in doGet', error);
    }
}

/**
 * Creates standardized JSON responses and handles errors
 */
function createJsonResponse(data) {
    return ContentService.createTextOutput(JSON.stringify(data)).setMimeType(ContentService.MimeType.JSON);
}

function handleError(context, error) {
    const errorDetails = {
        message: error.message || 'Unknown error',
        stack: error.stack || 'No stack trace available',
        timestamp: new Date().toISOString()
    };

    console.error(`${context}:`, errorDetails);

    // Return user-friendly error message while logging detailed info
    return createJsonResponse({
        success: false,
        error: `${context}: ${errorDetails.message}`,
        timestamp: errorDetails.timestamp
    });
}

/**
 * Calls Gemini API to extract structured data from SMS
 */
function getStructuredExpenseData(rawSmsMsg) {
    // Validate input
    if (!rawSmsMsg || typeof rawSmsMsg !== 'string' || rawSmsMsg.trim().length === 0) {
        console.error('Invalid SMS message provided to getStructuredExpenseData');
        return null;
    }

    const apiKey = PropertiesService.getScriptProperties().getProperty('GEMINI_API_KEY');
    if (!apiKey || apiKey.trim().length === 0) {
        console.error('GEMINI_API_KEY not found or empty in script properties');
        return null;
    }

    const payload = {
        contents: [{
            parts: [{
                text: `Analyze this SMS message and extract transaction information. Follow these STRICT rules for classification:

**FINANCIAL TRANSACTIONS (isFinanceMsg: true):**
Only consider as financial if the SMS describes a DEBIT or CREDIT transaction from/to YOUR OWN bank account or card:
- Money debited from your account (e.g., "Rs.500 debited from HDFC Bank A/c XX1234")
- Money credited to your account (e.g., "Rs.1000 credited to your SBI account")
- Card purchases/payments (e.g., "Rs.250 spent on HDFC card ending 1234 at Swiggy")
- UPI payments sent from your account (e.g., "Sent Rs.100 from HDFC Bank A/C to merchant")
- UPI payments received in your account (e.g., "Received Rs.200 in ICICI Bank A/C from John")

**NON-FINANCIAL (isFinanceMsg: false):**
Treat ALL of the following as non-financial:
- OTPs and verification codes
- Balance inquiries/updates (e.g., "Available balance is Rs.5000")
- Payment confirmations without actual debit/credit (e.g., "Payment successful", "We've received your payment")
- Transaction declined/failed messages
- Auto-pay reminders and due date notifications
- Money requests (someone requesting money from you)
- SIP/mutual fund transactions
- Promotional messages and offers
- Account statements and summaries
- Cheque status updates
- Any message that doesn't involve actual money movement from/to your account

**Data Extraction Rules:**
- For financial transactions: Extract account name, amount, purpose, UPI ID, reference number
- For non-financial: Set all financial fields to empty strings except category and summary
- Preserve account names exactly as written (e.g., "HDFC Bank A/c XX1234")
- Extract only numeric amount values without currency symbols
- Provide a 5-word summary of the message
- Categorize the message appropriately
- Note: Date/time will be automatically set to current date when logging

**Response Format:**
- If financial transaction: {"error": "", "isFinanceMsg": true, ...other fields}
- If non-financial: {"error": "", "isFinanceMsg": false, "account": "", "amount": 0, "purpose": "", "upiId": "", "reference": "", ...other fields}

Raw SMS: ${rawSmsMsg}`
            }]
        }],
        generationConfig: {
            responseMimeType: "application/json",
            responseSchema: RESPONSE_SCHEMA,
            temperature: 0.1 // Lower temperature for more consistent results
        }
    };

    const maxRetries = 3;
    let lastError = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`Calling Gemini API (attempt ${attempt}/${maxRetries})`);

            const response = UrlFetchApp.fetch(`${CONFIG.GEMINI_API_URL}?key=${apiKey}`, {
                method: 'POST',
                contentType: 'application/json',
                payload: JSON.stringify(payload),
                muteHttpExceptions: true
            });

            const responseCode = response.getResponseCode();
            const responseText = response.getContentText();

            if (responseCode !== 200) {
                const errorMsg = `API Error: ${responseCode} - ${responseText}`;
                console.error(errorMsg);
                lastError = new Error(errorMsg);

                // Don't retry on client errors (4xx)
                if (responseCode >= 400 && responseCode < 500) {
                    break;
                }
                continue;
            }

            const responseData = JSON.parse(responseText);
            const content = responseData.candidates?.[0]?.content?.parts?.[0]?.text;

            if (!content) {
                const errorMsg = 'Invalid API response structure - no content found';
                console.error(errorMsg, responseData);
                lastError = new Error(errorMsg);
                continue;
            }

            console.log('Gemini API response:', content);
            const parsedData = parseJsonContent(content);

            if (parsedData) {
                return parsedData;
            } else {
                lastError = new Error('Failed to parse JSON content from API response');
            }
        } catch (error) {
            console.error(`Error calling Gemini API (attempt ${attempt}):`, error);
            lastError = error;
        }

        // Wait before retry (exponential backoff)
        if (attempt < maxRetries) {
            Utilities.sleep(Math.pow(2, attempt) * 1000);
        }
    }

    console.error('All API attempts failed. Last error:', lastError);
    return null;
}

/**
 * Parses JSON content with multiple fallback strategies
 */
function parseJsonContent(content) {
    if (!content || typeof content !== 'string') {
        console.error('Invalid content provided to parseJsonContent:', content);
        return null;
    }

    const strategies = [
        content.trim(),
        content.match(/```json\s*([\s\S]*?)\s*```/)?.[1]?.trim(),
        content.match(/\{[\s\S]*\}/)?.[0]?.trim()
    ].filter(Boolean);

    for (const strategy of strategies) {
        try {
            const parsed = JSON.parse(strategy);
            console.log('Successfully parsed JSON using strategy:', strategy.substring(0, 100) + '...');
            return parsed; // Return the parsed object, not the string
        } catch (err) {
            console.log('Failed to parse JSON with strategy:', err.message);
            continue;
        }
    }

    console.error('No valid JSON found in response:', content);
    return null;
}

/**
 * Processes expense data and saves to appropriate sheet
 */
function processExpenseData(expenseData, rawSmsMsg) {
    try {
        // expenseData is now already a parsed object from parseJsonContent
        let data;
        if (typeof expenseData === 'string') {
            // Fallback: if somehow we still get a string, parse it
            data = JSON.parse(expenseData);
        } else if (typeof expenseData === 'object' && expenseData !== null) {
            data = expenseData;
        } else {
            throw new Error('Invalid expense data format');
        }

        // Validate required fields
        if (!data || typeof data !== 'object') {
            throw new Error('Expense data is not a valid object');
        }

        console.log('Processing expense data:', data);

        const spreadsheet = SpreadsheetApp.openByUrl(CONFIG.SPREADSHEET_URL);
        return (data.isFinanceMsg === true && !data.error)
            ? handleFinancialTransaction(spreadsheet, rawSmsMsg, data)
            : handleNonFinancialMessage(spreadsheet, rawSmsMsg, data);
    } catch (error) {
        console.error('Error processing expense data:', error);
        return { success: false, error: `Processing error: ${error.message}` };
    }
}

/**
 * Creates standardized response objects
 */
function createResponse(success, isFinanceMsg, data, options = {}) {
    return {
        success,
        rowsAdded: success ? 1 : 0,
        isFinanceMsg,
        message: options.message || `${isFinanceMsg ? 'Financial' : 'Non-financial'} SMS logged: ${options.rawSmsMsg?.substring(0, 50)}...`,
        data,
        ...(options.monthSheet && { monthSheet: options.monthSheet }),
        ...(options.loggedTo && { loggedTo: options.loggedTo })
    };
}

/**
 * Handles non-financial SMS messages
 */
function handleNonFinancialMessage(spreadsheet, rawSmsMsg, data) {
    const sheet = getOrCreateSheet(spreadsheet, "Others", CONFIG.HEADERS.OTHERS);

    if (rawSmsMsg.includes('unread_notes')) {
        return createResponse(false, false, data, { rawSmsMsg, loggedTo: "Others" });
    }

    // Always use current date for logging
    const currentDate = new Date();

    sheet.appendRow([
        rawSmsMsg || 'NA',
        data?.category || 'Unknown',
        data?.summary || '',
        currentDate.toLocaleDateString(),
        currentDate.toLocaleTimeString()
    ]);
    console.log("Non-financial message logged to Others sheet");
    return createResponse(true, false, data, { rawSmsMsg, loggedTo: "Others" });
}

/**
 * Handles financial transaction messages
 */
function handleFinancialTransaction(spreadsheet, rawSmsMsg, data) {
    // Always use current date for logging
    const currentDate = new Date();
    const monthName = CONFIG.MONTH_NAMES[currentDate.getMonth()];
    const sheet = getOrCreateSheet(spreadsheet, monthName, CONFIG.HEADERS.EXPENSE);

    // Validate and sanitize data before logging
    const sanitizedData = {
        summary: data.summary || '',
        account: data.account || 'NA',
        amount: data.amount || 'NA',
        purpose: data.purpose || 'NA',
        upiId: data.upiId || 'NA',
        type: data.type || 'NA',
        reference: data.reference || 'NA'
    };

    sheet.appendRow([
        sanitizedData.summary,
        sanitizedData.account,
        sanitizedData.amount,
        sanitizedData.purpose,
        sanitizedData.upiId,
        sanitizedData.type,
        sanitizedData.reference,
        currentDate.toLocaleDateString(),
        currentDate.toLocaleTimeString(),
        rawSmsMsg || 'NA'
    ]);

    console.log(`Financial transaction logged to ${monthName} sheet:`, sanitizedData);
    return createResponse(true, true, {
        rawSmsMsg,
        account: sanitizedData.account,
        amount: sanitizedData.amount,
        type: sanitizedData.type,
        purpose: sanitizedData.purpose
    }, { monthSheet: monthName });
}

/**
 * Gets existing sheet or creates new one with headers
 */
function getOrCreateSheet(spreadsheet, sheetName, headers) {
    let sheet = spreadsheet.getSheetByName(sheetName);
    if (!sheet) {
        sheet = spreadsheet.insertSheet(sheetName);
        sheet.appendRow(headers);
        sheet.getRange(1, 1, 1, headers.length).setFontWeight("bold");
        console.log(`Created new sheet: ${sheetName}`);
    }
    return sheet;
}

// ============================================================================
// NOTE: Test functions and test cases have been moved to test-cases.js
// To run tests, use the functions in test-cases.js:
// - testScript() - Run all test cases
// - testSMSClassification(smsMessage) - Test a single SMS
// - testByCategory(category) - Test specific category
// - clearTestData() - Clear test data from spreadsheet
// ============================================================================





