const geminiModels = [
    "gemini-2.0-flash",
    "gemini-2.0-flash-001",
    "gemini-2.5-flash-preview-05-20"
]
const CONFIG = {
    SPREADSHEET_URL: 'https://docs.google.com/spreadsheets/d/1y-Kit9o2Rv9wlJq4AqJAXaYChMa7WxD52-MqNQrVxDw/edit',
    GEMINI_API_URL: `https://generativelanguage.googleapis.com/v1beta/models/${geminiModels[1]}:generateContent`,
    MONTH_NAMES: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
    HEADERS: {
        EXPENSE: ["Summary", "Account Name", "Amount", "Purpose", "UPI ID", "Type", "Reference", "Date", "Time", "Raw Message"],
        OTHERS: ["Raw Message", "Category", "Summary", "Date", "Time"]
    }
};

// Response schema for Gemini API
const RESPONSE_SCHEMA = {
    type: "object",
    properties: {
        account: {
            type: "string",
            description: "Bank account identifier (e.g., 'HDFC Bank A/c XX1234'). Empty string for non-financial messages."
        },
        name: {
            type: "string",
            description: "Name of person/entity in transaction. Empty string if not applicable."
        },
        error: {
            type: "string",
            description: "Error message if processing failed, empty string if successful"
        },
        amount: {
            type: "number",
            description: "Transaction amount as number. Use 0 for non-financial messages."
        },
        purpose: {
            type: "string",
            description: "Transaction purpose/description. Empty string for non-financial messages."
        },
        upiId: {
            type: "string",
            description: "UPI ID if applicable, empty string otherwise"
        },
        type: {
            type: "string",
            enum: ["debit", "credit", ""],
            description: "Transaction type: 'debit' for money going out, 'credit' for money coming in, empty string for non-financial"
        },
        isFinanceMsg: {
            type: "boolean",
            description: "true only for actual debit/credit transactions from/to user's own accounts, false for everything else"
        },
        reference: {
            type: "string",
            description: "Transaction reference number. Empty string for non-financial messages."
        },
        summary: {
            type: "string",
            description: "Brief 5-word summary of the message content"
        },
        category: {
            type: "string",
            description: "Message category: 'finance' for actual transactions, or specific category like 'otp', 'balance_inquiry', 'promotional', 'sip', 'payment_confirmation', etc."
        }
    },
    required: ["account", "name", "error", "amount", "purpose", "upiId", "type", "isFinanceMsg", "reference", "category", "summary"]
};

/**
 * Main entry point for the web app
 */
function doGet(e) {
    try {
        // Validate input parameters
        if (!e || !e.parameters) {
            return createJsonResponse({ success: false, error: 'No parameters provided' });
        }

        const rawSmsMsg = e.parameters.rawMsg;
        if (!rawSmsMsg) {
            return createJsonResponse({ success: false, error: 'No rawMsg parameter provided' });
        }

        // Handle array parameter (common in Google Apps Script)
        const smsMessage = Array.isArray(rawSmsMsg) ? rawSmsMsg[0] : rawSmsMsg.toString();

        if (!smsMessage || smsMessage.trim().length === 0) {
            return createJsonResponse({ success: false, error: 'Empty SMS message provided' });
        }

        console.log('Processing SMS message:', smsMessage.substring(0, 100) + '...');

        const expenseData = getStructuredExpenseData(smsMessage);
        if (!expenseData) {
            return createJsonResponse({ success: false, error: 'Failed to extract structured data from SMS' });
        }

        return createJsonResponse(processExpenseData(expenseData, smsMessage));
    } catch (error) {
        return handleError('Error in doGet', error);
    }
}

/**
 * Creates standardized JSON responses and handles errors
 */
function createJsonResponse(data) {
    return ContentService.createTextOutput(JSON.stringify(data)).setMimeType(ContentService.MimeType.JSON);
}

function handleError(context, error) {
    const errorDetails = {
        message: error.message || 'Unknown error',
        stack: error.stack || 'No stack trace available',
        timestamp: new Date().toISOString()
    };

    console.error(`${context}:`, errorDetails);

    // Return user-friendly error message while logging detailed info
    return createJsonResponse({
        success: false,
        error: `${context}: ${errorDetails.message}`,
        timestamp: errorDetails.timestamp
    });
}

/**
 * Calls Gemini API to extract structured data from SMS
 */
function getStructuredExpenseData(rawSmsMsg) {
    // Validate input
    if (!rawSmsMsg || typeof rawSmsMsg !== 'string' || rawSmsMsg.trim().length === 0) {
        console.error('Invalid SMS message provided to getStructuredExpenseData');
        return null;
    }

    const apiKey = PropertiesService.getScriptProperties().getProperty('GEMINI_API_KEY');
    if (!apiKey || apiKey.trim().length === 0) {
        console.error('GEMINI_API_KEY not found or empty in script properties');
        return null;
    }

    const payload = {
        contents: [{
            parts: [{
                text: `Analyze this SMS message and extract transaction information. Follow these STRICT rules for classification:

**FINANCIAL TRANSACTIONS (isFinanceMsg: true):**
Only consider as financial if the SMS describes a DEBIT or CREDIT transaction from/to YOUR OWN bank account or card:
- Money debited from your account (e.g., "Rs.500 debited from HDFC Bank A/c XX1234")
- Money credited to your account (e.g., "Rs.1000 credited to your SBI account")
- Card purchases/payments (e.g., "Rs.250 spent on HDFC card ending 1234 at Swiggy")
- UPI payments sent from your account (e.g., "Sent Rs.100 from HDFC Bank A/C to merchant")
- UPI payments received in your account (e.g., "Received Rs.200 in ICICI Bank A/C from John")

**NON-FINANCIAL (isFinanceMsg: false):**
Treat ALL of the following as non-financial:
- OTPs and verification codes
- Balance inquiries/updates (e.g., "Available balance is Rs.5000")
- Payment confirmations without actual debit/credit (e.g., "Payment successful", "We've received your payment")
- Transaction declined/failed messages
- Auto-pay reminders and due date notifications
- Money requests (someone requesting money from you)
- SIP/mutual fund transactions
- Promotional messages and offers
- Account statements and summaries
- Cheque status updates
- Any message that doesn't involve actual money movement from/to your account

**Data Extraction Rules:**
- For financial transactions: Extract account name, amount, purpose, UPI ID, reference number
- For non-financial: Set all financial fields to empty strings except category and summary
- Preserve account names exactly as written (e.g., "HDFC Bank A/c XX1234")
- Extract only numeric amount values without currency symbols
- If transaction date is mentioned in SMS, note it for date extraction
- Provide a 5-word summary of the message
- Categorize the message appropriately

**Response Format:**
- If financial transaction: {"error": "", "isFinanceMsg": true, ...other fields}
- If non-financial: {"error": "", "isFinanceMsg": false, "account": "", "amount": 0, "purpose": "", "upiId": "", "reference": "", ...other fields}

Raw SMS: ${rawSmsMsg}`
            }]
        }],
        generationConfig: {
            responseMimeType: "application/json",
            responseSchema: RESPONSE_SCHEMA,
            temperature: 0.1 // Lower temperature for more consistent results
        }
    };

    const maxRetries = 3;
    let lastError = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`Calling Gemini API (attempt ${attempt}/${maxRetries})`);

            const response = UrlFetchApp.fetch(`${CONFIG.GEMINI_API_URL}?key=${apiKey}`, {
                method: 'POST',
                contentType: 'application/json',
                payload: JSON.stringify(payload),
                muteHttpExceptions: true
            });

            const responseCode = response.getResponseCode();
            const responseText = response.getContentText();

            if (responseCode !== 200) {
                const errorMsg = `API Error: ${responseCode} - ${responseText}`;
                console.error(errorMsg);
                lastError = new Error(errorMsg);

                // Don't retry on client errors (4xx)
                if (responseCode >= 400 && responseCode < 500) {
                    break;
                }
                continue;
            }

            const responseData = JSON.parse(responseText);
            const content = responseData.candidates?.[0]?.content?.parts?.[0]?.text;

            if (!content) {
                const errorMsg = 'Invalid API response structure - no content found';
                console.error(errorMsg, responseData);
                lastError = new Error(errorMsg);
                continue;
            }

            console.log('Gemini API response:', content);
            const parsedData = parseJsonContent(content);

            if (parsedData) {
                return parsedData;
            } else {
                lastError = new Error('Failed to parse JSON content from API response');
            }
        } catch (error) {
            console.error(`Error calling Gemini API (attempt ${attempt}):`, error);
            lastError = error;
        }

        // Wait before retry (exponential backoff)
        if (attempt < maxRetries) {
            Utilities.sleep(Math.pow(2, attempt) * 1000);
        }
    }

    console.error('All API attempts failed. Last error:', lastError);
    return null;
}

/**
 * Parses JSON content with multiple fallback strategies
 */
function parseJsonContent(content) {
    if (!content || typeof content !== 'string') {
        console.error('Invalid content provided to parseJsonContent:', content);
        return null;
    }

    const strategies = [
        content.trim(),
        content.match(/```json\s*([\s\S]*?)\s*```/)?.[1]?.trim(),
        content.match(/\{[\s\S]*\}/)?.[0]?.trim()
    ].filter(Boolean);

    for (const strategy of strategies) {
        try {
            const parsed = JSON.parse(strategy);
            console.log('Successfully parsed JSON using strategy:', strategy.substring(0, 100) + '...');
            return parsed; // Return the parsed object, not the string
        } catch (err) {
            console.log('Failed to parse JSON with strategy:', err.message);
            continue;
        }
    }

    console.error('No valid JSON found in response:', content);
    return null;
}

/**
 * Processes expense data and saves to appropriate sheet
 */
function processExpenseData(expenseData, rawSmsMsg) {
    try {
        // expenseData is now already a parsed object from parseJsonContent
        let data;
        if (typeof expenseData === 'string') {
            // Fallback: if somehow we still get a string, parse it
            data = JSON.parse(expenseData);
        } else if (typeof expenseData === 'object' && expenseData !== null) {
            data = expenseData;
        } else {
            throw new Error('Invalid expense data format');
        }

        // Validate required fields
        if (!data || typeof data !== 'object') {
            throw new Error('Expense data is not a valid object');
        }

        console.log('Processing expense data:', data);

        const spreadsheet = SpreadsheetApp.openByUrl(CONFIG.SPREADSHEET_URL);
        return (data.isFinanceMsg === true && !data.error)
            ? handleFinancialTransaction(spreadsheet, rawSmsMsg, data)
            : handleNonFinancialMessage(spreadsheet, rawSmsMsg, data);
    } catch (error) {
        console.error('Error processing expense data:', error);
        return { success: false, error: `Processing error: ${error.message}` };
    }
}

/**
 * Creates standardized response objects
 */
function createResponse(success, isFinanceMsg, data, options = {}) {
    return {
        success,
        rowsAdded: success ? 1 : 0,
        isFinanceMsg,
        message: options.message || `${isFinanceMsg ? 'Financial' : 'Non-financial'} SMS logged: ${options.rawSmsMsg?.substring(0, 50)}...`,
        data,
        ...(options.monthSheet && { monthSheet: options.monthSheet }),
        ...(options.loggedTo && { loggedTo: options.loggedTo })
    };
}

/**
 * Handles non-financial SMS messages
 */
function handleNonFinancialMessage(spreadsheet, rawSmsMsg, data) {
    const sheet = getOrCreateSheet(spreadsheet, "Others", CONFIG.HEADERS.OTHERS);

    if (rawSmsMsg.includes('unread_notes')) {
        return createResponse(false, false, data, { rawSmsMsg, loggedTo: "Others" });
    }

    // Always use current date for logging
    const currentDate = new Date();

    sheet.appendRow([
        rawSmsMsg || 'NA',
        data?.category || 'Unknown',
        data?.summary || '',
        currentDate.toLocaleDateString(),
        currentDate.toLocaleTimeString()
    ]);
    console.log("Non-financial message logged to Others sheet");
    return createResponse(true, false, data, { rawSmsMsg, loggedTo: "Others" });
}

/**
 * Handles financial transaction messages
 */
function handleFinancialTransaction(spreadsheet, rawSmsMsg, data) {
    // Always use current date for logging
    const currentDate = new Date();
    const monthName = CONFIG.MONTH_NAMES[currentDate.getMonth()];
    const sheet = getOrCreateSheet(spreadsheet, monthName, CONFIG.HEADERS.EXPENSE);

    // Validate and sanitize data before logging
    const sanitizedData = {
        summary: data.summary || '',
        account: data.account || 'NA',
        amount: data.amount || 'NA',
        purpose: data.purpose || 'NA',
        upiId: data.upiId || 'NA',
        type: data.type || 'NA',
        reference: data.reference || 'NA'
    };

    sheet.appendRow([
        sanitizedData.summary,
        sanitizedData.account,
        sanitizedData.amount,
        sanitizedData.purpose,
        sanitizedData.upiId,
        sanitizedData.type,
        sanitizedData.reference,
        currentDate.toLocaleDateString(),
        currentDate.toLocaleTimeString(),
        rawSmsMsg || 'NA'
    ]);

    console.log(`Financial transaction logged to ${monthName} sheet:`, sanitizedData);
    return createResponse(true, true, {
        rawSmsMsg,
        account: sanitizedData.account,
        amount: sanitizedData.amount,
        type: sanitizedData.type,
        purpose: sanitizedData.purpose
    }, { monthSheet: monthName });
}

/**
 * Gets existing sheet or creates new one with headers
 */
function getOrCreateSheet(spreadsheet, sheetName, headers) {
    let sheet = spreadsheet.getSheetByName(sheetName);
    if (!sheet) {
        sheet = spreadsheet.insertSheet(sheetName);
        sheet.appendRow(headers);
        sheet.getRange(1, 1, 1, headers.length).setFontWeight("bold");
        console.log(`Created new sheet: ${sheetName}`);
    }
    return sheet;
}

/**
 * Extracts transaction date from SMS content
 */
function extractTransactionDateFromSMS(smsContent) {
    if (!smsContent || typeof smsContent !== 'string') {
        return new Date();
    }

    // Common date patterns in SMS messages
    const datePatterns = [
        // DD-MM-YY, DD/MM/YY, DD-MM-YYYY, DD/MM/YYYY
        /(?:on\s+)?(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2,4})/i,
        // DD-MMM-YY, DD-MMM-YYYY (e.g., 01-JUN-25, 02-Jun-2025)
        /(?:on\s+)?(\d{1,2})-([A-Za-z]{3})-(\d{2,4})/i,
        // MMM DD, YYYY (e.g., Jun 02, 2025)
        /([A-Za-z]{3})\s+(\d{1,2}),?\s+(\d{4})/i,
        // DD MMM YY (e.g., 02 Jun 25)
        /(\d{1,2})\s+([A-Za-z]{3})\s+(\d{2,4})/i
    ];

    for (const pattern of datePatterns) {
        const match = smsContent.match(pattern);
        if (match) {
            try {
                const parsedDate = parseMatchedDate(match, pattern);
                if (parsedDate && !isNaN(parsedDate.getTime())) {
                    console.log('Extracted date from SMS:', parsedDate.toDateString());
                    return parsedDate;
                }
            } catch (error) {
                console.log('Error parsing matched date:', error.message);
                continue;
            }
        }
    }

    console.log('No valid date found in SMS, using current date');
    return new Date();
}

/**
 * Parses matched date based on the pattern used
 */
function parseMatchedDate(match, pattern) {
    const patternStr = pattern.toString();

    if (patternStr.includes('[A-Za-z]{3}')) {
        // Handle month name patterns
        if (patternStr.includes('([A-Za-z]{3})\\s+(\\d{1,2})')) {
            // MMM DD, YYYY format
            const [, monthStr, day, year] = match;
            return parseMonthNameDate(day, monthStr, year);
        } else {
            // DD-MMM-YY or DD MMM YY format
            const [, day, monthStr, year] = match;
            return parseMonthNameDate(day, monthStr, year);
        }
    } else {
        // Handle numeric date patterns DD/MM/YY
        const [, day, month, yearStr] = match;
        let year = parseInt(yearStr, 10);
        if (year < 100) {
            year += year < 50 ? 2000 : 1900;
        }
        return new Date(year, parseInt(month, 10) - 1, parseInt(day, 10));
    }
}

/**
 * Parses dates with month names
 */
function parseMonthNameDate(day, monthStr, year) {
    const monthMap = {
        'jan': 0, 'feb': 1, 'mar': 2, 'apr': 3, 'may': 4, 'jun': 5,
        'jul': 6, 'aug': 7, 'sep': 8, 'oct': 9, 'nov': 10, 'dec': 11
    };

    const monthIndex = monthMap[monthStr.toLowerCase()];
    if (monthIndex === undefined) {
        throw new Error(`Unknown month: ${monthStr}`);
    }

    let yearNum = parseInt(year, 10);
    if (yearNum < 100) {
        yearNum += yearNum < 50 ? 2000 : 1900;
    }

    return new Date(yearNum, monthIndex, parseInt(day, 10));
}

/**
 * Parses transaction date with multiple format support (legacy function, kept for compatibility)
 */
function parseTransactionDate(dateTimeString) {
    if (!dateTimeString) return new Date();

    const dateMatch = dateTimeString.match(/(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2,4})/);
    if (dateMatch) {
        const [, day, month, yearStr] = dateMatch;
        let year = parseInt(yearStr, 10);
        if (year < 100) year += year < 50 ? 2000 : 1900;
        return new Date(year, parseInt(month, 10) - 1, parseInt(day, 10));
    }

    const parsedDate = new Date(dateTimeString);
    return isNaN(parsedDate.getTime()) ? new Date() : parsedDate;
}

/**
 * Test function for development
 */
function testScript() {
    const testMessages = [
        // NON-FINANCIAL CASES
        {
            sms: `Your SIP Purchase in Folio ********/34 under HDFC Focused 30 Fund - Gro for Rs. 9,999.50 has been processed at the NAV of 225.301 for 44.383 units and 02-Jun-2025. Your smart statement https://shrtsms.in/HDFCMF/15vCB93. Enter your PAN as the password. Sincerely, HDFCMF`,
            expected: 'Non-financial (SIP transaction)'
        },
        {
            sms: `OTP is 101119 for txn of INR 251.00 at Swiggy Limi on HDFC Bank card ending 3934. Valid till  06:36. Do not share OTP for security reasons`,
            expected: 'Non-financial (OTP)'
        },
        {
            sms: `Available Bal in HDFC Bank A/c XX3433 as on yesterday:01-JUN-25 is INR 6,593.34. Cheques are subject to clearing.For updated A/C Bal dial ***********`,
            expected: 'Non-financial (Balance inquiry)'
        },
        {
            sms: `Payment of Rs.2500 has been received successfully. Thank you for your payment. Reference: PAY123456`,
            expected: 'Non-financial (Payment confirmation)'
        },
        {
            sms: `Your payment of Rs.1500 is due on 15-Jun-2025. Please pay to avoid late charges. Pay now: bit.ly/pay123`,
            expected: 'Non-financial (Payment reminder)'
        },
        {
            sms: `TXN DECLINED: Your transaction of Rs.500 at Amazon has been declined due to insufficient balance. HDFC Bank`,
            expected: 'Non-financial (Declined transaction)'
        },
        {
            sms: `John Doe has requested Rs.2000 from you via UPI. Accept or decline in your banking app.`,
            expected: 'Non-financial (Money request)'
        },

        // FINANCIAL CASES (Actual debit/credit from user's account)
        {
            sms: `Sent Rs.4451.80
From HDFC Bank A/C x3433
To JUPITER UPI COLLECT
On 30/05/25
Ref ************
Not You?
Call ***********/SMS BLOCK UPI to **********`,
            expected: 'Financial (Debit - UPI payment sent)'
        },
        {
            sms: `Amt Sent Rs.3000.00
From HDFC Bank A/C *3433
To modi shrikant hardik
On 23-05
Ref ************
Not You? Call ***********/SMS BLOCK UPI to **********`,
            expected: 'Financial (Debit - UPI transfer)'
        },
        {
            sms: `UPDATE: INR 11,500.00 debited from HDFC Bank XX3433 on 01-JUN-25. Info: ACH D- INVACIALABSPRIVATELI-INVACIALABQb. Avl bal:INR 60,093.34`,
            expected: 'Financial (Debit - ACH debit)'
        },
        {
            sms: `Rs.5000.00 credited to your ICICI Bank A/c XX9876 on 15-Jun-25. From: SALARY CREDIT. Ref: SAL123456`,
            expected: 'Financial (Credit - Salary)'
        },
        {
            sms: `INR 250.00 spent on HDFC Bank card ending 1234 at SWIGGY on 10-Jun-25. Available limit: Rs.45000`,
            expected: 'Financial (Debit - Card purchase)'
        }
    ];

    console.log('=== Starting SMS Processing Tests ===');

    let passedTests = 0;
    let failedTests = 0;

    testMessages.forEach((testCase, index) => {
        try {
            console.log(`\n--- Test ${index + 1}: ${testCase.expected} ---`);
            console.log('SMS:', testCase.sms.substring(0, 80) + '...');

            // Test full processing
            const result = doGet({ parameters: { rawMsg: [testCase.sms] } });
            const resultData = JSON.parse(result.getContent());

            // Validate classification
            const expectedFinancial = testCase.expected.startsWith('Financial');
            const actualFinancial = resultData.isFinanceMsg;
            const classificationCorrect = expectedFinancial === actualFinancial;

            console.log('Result:', {
                success: resultData.success,
                isFinanceMsg: actualFinancial,
                expected: expectedFinancial,
                classification: classificationCorrect ? '✅ CORRECT' : '❌ WRONG',
                category: resultData.data?.category || 'N/A',
                amount: resultData.data?.amount || 0,
                loggedDate: new Date().toLocaleDateString()
            });

            if (classificationCorrect && resultData.success) {
                passedTests++;
            } else {
                failedTests++;
                if (!resultData.success) {
                    console.error('Processing failed:', resultData.error);
                }
            }
        } catch (error) {
            console.error(`Test ${index + 1} failed with error:`, error.message);
            failedTests++;
        }
    });

    console.log(`\n=== Tests Completed ===`);
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📊 Success Rate: ${((passedTests / (passedTests + failedTests)) * 100).toFixed(1)}%`);
}

/**
 * Utility function to clear all test data (use with caution)
 */
function clearTestData() {
    const spreadsheet = SpreadsheetApp.openByUrl(CONFIG.SPREADSHEET_URL);
    spreadsheet.getSheets().forEach(sheet => {
        if (sheet.getName() !== 'Sheet1') spreadsheet.deleteSheet(sheet);
    });
    console.log('Test data cleared');
}