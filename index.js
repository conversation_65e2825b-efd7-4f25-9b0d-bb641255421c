const geminiModels = [
    "gemini-2.0-flash",
    "gemini-2.0-flash-001",
    "gemini-2.5-flash-preview-05-20"
]
const CONFIG = {
    SPREADSHEET_URL: 'https://docs.google.com/spreadsheets/d/1y-Kit9o2Rv9wlJq4AqJAXaYChMa7WxD52-MqNQrVxDw/edit',
    GEMINI_API_URL: `https://generativelanguage.googleapis.com/v1beta/models/${geminiModels[1]}:generateContent`,
    MONTH_NAMES: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
    HEADERS: {
        EXPENSE: ["Summary", "Account Name", "Amount", "Purpose", "UPI ID", "Type", "Reference", "Date", "Time", "Raw Message"],
        OTHERS: ["Raw Message", "Category", "Summary", "Date", "Time"]
    }
};

// Response schema for Gemini API
const RESPONSE_SCHEMA = {
    type: "object",
    properties: {
        account: { type: "string", description: "Bank account number or identifier" },
        name: { type: "string", description: "Name of person/entity in transaction" },
        error: { type: "string", description: "Error message if failed, empty string if successful" },
        amount: { type: "number", description: "Transaction amount as number" },
        purpose: { type: "string", description: "Transaction purpose/description" },
        upiId: { type: "string", description: "UPI ID if applicable, empty string otherwise" },
        type: { type: "string", enum: ["debit", "credit", "bill_payment"], description: "Transaction type" },
        isFinanceMsg: { type: "boolean", description: "Whether this SMS is related to financial transaction" },
        reference: { type: "string", description: "Reference number for the transaction" },
        summary: { type: "string", description: "Summary for the message in 5 words" },
        category: { type: "string", description: "SMS category. Examples: entertainment, utility, food,finance etc." }
    },
    required: ["account", "name", "error", "amount", "purpose", "upiId", "type", "isFinanceMsg", "reference", "category", "summary"]
};

/**
 * Main entry point for the web app
 */
function doGet(e) {
    try {
        const rawSmsMsg = e?.parameters?.rawMsg?.toString();
        if (!rawSmsMsg) return createJsonResponse({ success: false, error: 'No rawMsg parameter provided' });

        const expenseData = getStructuredExpenseData(rawSmsMsg);
        if (!expenseData) return createJsonResponse({ success: false, error: 'Failed to process SMS data' });

        return createJsonResponse(processExpenseData(expenseData, rawSmsMsg));
    } catch (error) {
        return handleError('Error in doGet', error);
    }
}

/**
 * Creates standardized JSON responses and handles errors
 */
function createJsonResponse(data) {
    return ContentService.createTextOutput(JSON.stringify(data)).setMimeType(ContentService.MimeType.JSON);
}

function handleError(context, error) {
    console.error(`${context}:`, error);
    return createJsonResponse({ success: false, error: `${context}: ${error.message}` });
}

/**
 * Calls Gemini API to extract structured data from SMS
 */
function getStructuredExpenseData(rawSmsMsg) {
    const apiKey = PropertiesService.getScriptProperties().getProperty('GEMINI_API_KEY');
    if (!apiKey) {
        console.error('GEMINI_API_KEY not found in script properties');
        return null;
    }

    const payload = {
        contents: [{
            parts: [{
                text: `Extract only actual debit, credit, or purchase transactions from this SMS. 
    Very strictly follow this instructions
Treat OTPs, balance updates,payment confirmations (e.g., "we've received your payment"),auto pay reminders, requests for money(has requested money), declined transactions ( e.g  TXN DECLINED) and SIP transactions as non-financial. 
For such cases, return: {"error": true, "isFinanceMsg": false}. 
Preserve account names as-is (e.g., "Kotak Bank AC X1234"). 
Raw SMS: ${rawSmsMsg}`
            }]
        }],
        generationConfig: { responseMimeType: "application/json", responseSchema: RESPONSE_SCHEMA }
    };

    try {
        const response = UrlFetchApp.fetch(`${CONFIG.GEMINI_API_URL}?key=${apiKey}`, {
            method: 'POST',
            contentType: 'application/json',
            payload: JSON.stringify(payload),
            muteHttpExceptions: true
        });

        if (response.getResponseCode() !== 200) {
            console.error(`API Error: ${response.getResponseCode()} - ${response.getContentText()}`);
            return null;
        }

        const content = JSON.parse(response.getContentText()).candidates?.[0]?.content?.parts?.[0]?.text;
        if (!content) {
            console.error('Invalid API response structure');
            return null;
        }

        console.log('Gemini API response:', content);
        return parseJsonContent(content);
    } catch (error) {
        console.error('Error calling Gemini API:', error);
        return null;
    }
}

/**
 * Parses JSON content with multiple fallback strategies
 */
function parseJsonContent(content) {
    const strategies = [
        content,
        content.match(/```json\s*([\s\S]*?)\s*```/)?.[1],
        content.match(/\{[\s\S]*\}/)?.[0]
    ].filter(Boolean);

    for (const strategy of strategies) {
        try {
            JSON.parse(strategy);
            return strategy;
        } catch (err) {
            continue;
        }
    }

    console.error('No valid JSON found in response:', content);
    return null;
}

/**
 * Processes expense data and saves to appropriate sheet
 */
function processExpenseData(expenseJsonData, rawSmsMsg) {
    try {
        const data = JSON.parse(expenseJsonData);
        console.log('Parsed expense data:', data);

        const spreadsheet = SpreadsheetApp.openByUrl(CONFIG.SPREADSHEET_URL);
        return (data.isFinanceMsg === true && !data.error)
            ? handleFinancialTransaction(spreadsheet, rawSmsMsg, data)
            : handleNonFinancialMessage(spreadsheet, rawSmsMsg, data);
    } catch (error) {
        console.error('Error processing expense data:', error);
        return { success: false, error: `Processing error: ${error.message}` };
    }
}

/**
 * Creates standardized response objects
 */
function createResponse(success, isFinanceMsg, data, options = {}) {
    return {
        success,
        rowsAdded: success ? 1 : 0,
        isFinanceMsg,
        message: options.message || `${isFinanceMsg ? 'Financial' : 'Non-financial'} SMS logged: ${options.rawSmsMsg?.substring(0, 50)}...`,
        data,
        ...(options.monthSheet && { monthSheet: options.monthSheet }),
        ...(options.loggedTo && { loggedTo: options.loggedTo })
    };
}

/**
 * Handles non-financial SMS messages
 */
function handleNonFinancialMessage(spreadsheet, rawSmsMsg, data) {
    const sheet = getOrCreateSheet(spreadsheet, "Others", CONFIG.HEADERS.OTHERS);

    if (rawSmsMsg.includes('unread_notes')) {
        return createResponse(false, false, data, { rawSmsMsg, loggedTo: "Others" });
    }

    const transactionDate = parseTransactionDate(new Date().toString());

    sheet.appendRow([rawSmsMsg || 'NA', data?.category || 'Unknown', data?.summary || '', transactionDate.toLocaleDateString(),
    transactionDate.toLocaleTimeString()]);
    console.log("Non-financial message logged to Others sheet");
    return createResponse(true, false, data, { rawSmsMsg, loggedTo: "Others" });
}

/**
 * Handles financial transaction messages
 */
function handleFinancialTransaction(spreadsheet, rawSmsMsg, data) {
    const transactionDate = parseTransactionDate(new Date().toString());
    const monthName = CONFIG.MONTH_NAMES[transactionDate.getMonth()];
    const sheet = getOrCreateSheet(spreadsheet, monthName, CONFIG.HEADERS.EXPENSE);

    sheet.appendRow([
        data.summary || '', data.account || 'NA', data.amount || 'NA', data.purpose || 'NA', data.upiId || 'NA',
        data.type || 'NA', data.reference || 'NA', transactionDate.toLocaleDateString(),
        transactionDate.toLocaleTimeString(), rawSmsMsg || 'NA'
    ]);

    console.log(`Financial transaction logged to ${monthName} sheet`);
    return createResponse(true, true, { rawSmsMsg, account: data.account, amount: data.amount, type: data.type, purpose: data.purpose }, { monthSheet: monthName });
}

/**
 * Gets existing sheet or creates new one with headers
 */
function getOrCreateSheet(spreadsheet, sheetName, headers) {
    let sheet = spreadsheet.getSheetByName(sheetName);
    if (!sheet) {
        sheet = spreadsheet.insertSheet(sheetName);
        sheet.appendRow(headers);
        sheet.getRange(1, 1, 1, headers.length).setFontWeight("bold");
        console.log(`Created new sheet: ${sheetName}`);
    }
    return sheet;
}

/**
 * Parses transaction date with multiple format support
 */
function parseTransactionDate(dateTimeString) {
    if (!dateTimeString) return new Date();

    const dateMatch = dateTimeString.match(/(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2,4})/);
    if (dateMatch) {
        const [, day, month, yearStr] = dateMatch;
        let year = parseInt(yearStr, 10);
        if (year < 100) year += year < 50 ? 2000 : 1900;
        return new Date(year, parseInt(month, 10) - 1, parseInt(day, 10));
    }

    const parsedDate = new Date(dateTimeString);
    return isNaN(parsedDate.getTime()) ? new Date() : parsedDate;
}

/**
 * Test function for development
 */
function testScript() {
    const testMessages = [
        `Your SIP Purchase in Folio ********/34 under HDFC Focused 30 Fund - Gro for Rs. 9,999.50 has been processed at the NAV of 225.301 for 44.383 units and 02-Jun-2025. Your smart statement https://shrtsms.in/HDFCMF/15vCB93. Enter your PAN as the password. Sincerely, HDFCMF`,
        `OTP is 101119 for txn of INR 251.00 at Swiggy Limi on HDFC Bank card ending 3934. Valid till  06:36. Do not share OTP for security reasons`,
        `Available Bal in HDFC Bank A/c XX3433 as on yesterday:01-JUN-25 is INR 6,593.34. Cheques are subject to clearing.For updated A/C Bal dial ***********`,
        `Sent Rs.4451.80
From HDFC Bank A/C x3433
To JUPITER UPI COLLECT
On 30/05/25
Ref ************
Not You?
Call ***********/SMS BLOCK UPI to **********`,
        `Amt Sent Rs.3000.00
From HDFC Bank A/C *3433
To modi shrikant hardik
On 23-05
Ref ************
Not You? Call ***********/SMS BLOCK UPI to **********`,
        `UPDATE: INR 11,500.00 debited from HDFC Bank XX3433 on 01-JUN-25. Info: ACH D- INVACIALABSPRIVATELI-INVACIALABQb. Avl bal:INR 60,093.34
`
    ];

    testMessages.forEach((sms, index) => {
        console.log(`\n--- Test ${index + 1} --- | ${sms}`);
        console.log('Result:', doGet({ parameters: { rawMsg: [sms] } }).getContent());
    });
}

/**
 * Utility function to clear all test data (use with caution)
 */
function clearTestData() {
    const spreadsheet = SpreadsheetApp.openByUrl(CONFIG.SPREADSHEET_URL);
    spreadsheet.getSheets().forEach(sheet => {
        if (sheet.getName() !== 'Sheet1') spreadsheet.deleteSheet(sheet);
    });
    console.log('Test data cleared');
}