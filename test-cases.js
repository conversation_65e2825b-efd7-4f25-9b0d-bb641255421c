/**
 * Test Cases for SMS Expense Tracker
 * 
 * This file contains comprehensive test cases to validate SMS classification
 * and processing functionality. It tests both financial and non-financial
 * message classification to ensure accuracy.
 */

// Test message data with expected classifications
const TEST_MESSAGES = [
    // NON-FINANCIAL CASES
    {
        sms: `Your SIP Purchase in Folio ********/34 under HDFC Focused 30 Fund - Gro for Rs. 9,999.50 has been processed at the NAV of 225.301 for 44.383 units and 02-Jun-2025. Your smart statement https://shrtsms.in/HDFCMF/15vCB93. Enter your PAN as the password. Sincerely, HDFCMF`,
        expected: 'Non-financial (SIP transaction)',
        category: 'sip'
    },
    {
        sms: `OTP is 101119 for txn of INR 251.00 at Swiggy Limi on HDFC Bank card ending 3934. Valid till  06:36. Do not share OTP for security reasons`,
        expected: 'Non-financial (OTP)',
        category: 'otp'
    },
    {
        sms: `Available Bal in HDFC Bank A/c XX3433 as on yesterday:01-JUN-25 is INR 6,593.34. Cheques are subject to clearing.For updated A/C Bal dial ***********`,
        expected: 'Non-financial (Balance inquiry)',
        category: 'balance_inquiry'
    },
    {
        sms: `Payment of Rs.2500 has been received successfully. Thank you for your payment. Reference: PAY123456`,
        expected: 'Non-financial (Payment confirmation)',
        category: 'payment_confirmation'
    },
    {
        sms: `Your payment of Rs.1500 is due on 15-Jun-2025. Please pay to avoid late charges. Pay now: bit.ly/pay123`,
        expected: 'Non-financial (Payment reminder)',
        category: 'payment_reminder'
    },
    {
        sms: `TXN DECLINED: Your transaction of Rs.500 at Amazon has been declined due to insufficient balance. HDFC Bank`,
        expected: 'Non-financial (Declined transaction)',
        category: 'declined_transaction'
    },
    {
        sms: `John Doe has requested Rs.2000 from you via UPI. Accept or decline in your banking app.`,
        expected: 'Non-financial (Money request)',
        category: 'money_request'
    },
    {
        sms: `Dear Customer, your HDFC Bank Credit Card statement for May 2025 is ready. Download from mobile app or netbanking.`,
        expected: 'Non-financial (Statement notification)',
        category: 'statement'
    },
    {
        sms: `Congratulations! You've earned 500 reward points on your HDFC Bank card. Redeem now at rewards.hdfcbank.com`,
        expected: 'Non-financial (Promotional)',
        category: 'promotional'
    },
    
    // FINANCIAL CASES (Actual debit/credit from user's account)
    {
        sms: `Sent Rs.4451.80
From HDFC Bank A/C x3433
To JUPITER UPI COLLECT
On 30/05/25
Ref ************
Not You?
Call ***********/SMS BLOCK UPI to **********`,
        expected: 'Financial (Debit - UPI payment sent)',
        category: 'finance'
    },
    {
        sms: `Amt Sent Rs.3000.00
From HDFC Bank A/C *3433
To modi shrikant hardik
On 23-05
Ref ************
Not You? Call ***********/SMS BLOCK UPI to **********`,
        expected: 'Financial (Debit - UPI transfer)',
        category: 'finance'
    },
    {
        sms: `UPDATE: INR 11,500.00 debited from HDFC Bank XX3433 on 01-JUN-25. Info: ACH D- INVACIALABSPRIVATELI-INVACIALABQb. Avl bal:INR 60,093.34`,
        expected: 'Financial (Debit - ACH debit)',
        category: 'finance'
    },
    {
        sms: `Rs.5000.00 credited to your ICICI Bank A/c XX9876 on 15-Jun-25. From: SALARY CREDIT. Ref: SAL123456`,
        expected: 'Financial (Credit - Salary)',
        category: 'finance'
    },
    {
        sms: `INR 250.00 spent on HDFC Bank card ending 1234 at SWIGGY on 10-Jun-25. Available limit: Rs.45000`,
        expected: 'Financial (Debit - Card purchase)',
        category: 'finance'
    },
    {
        sms: `Rs.1200.00 debited from SBI A/c XX5678 for electricity bill payment. Ref: ELEC789. Balance: Rs.25000`,
        expected: 'Financial (Debit - Bill payment)',
        category: 'finance'
    },
    {
        sms: `Amount Rs.750.00 credited to your Kotak Bank A/c XX4321 via NEFT from ABC Company. Ref: NEFT456`,
        expected: 'Financial (Credit - NEFT)',
        category: 'finance'
    }
];

/**
 * Main test function to validate SMS processing and classification
 */
function testScript() {
    console.log('=== Starting SMS Processing Tests ===');
    console.log(`Total test cases: ${TEST_MESSAGES.length}`);
    
    let passedTests = 0;
    let failedTests = 0;
    const failedCases = [];
    
    TEST_MESSAGES.forEach((testCase, index) => {
        try {
            console.log(`\n--- Test ${index + 1}: ${testCase.expected} ---`);
            console.log('SMS:', testCase.sms.substring(0, 80) + '...');
            
            // Test full processing using the main doGet function
            const result = doGet({ parameters: { rawMsg: [testCase.sms] } });
            const resultData = JSON.parse(result.getContent());
            
            // Validate classification
            const expectedFinancial = testCase.expected.startsWith('Financial');
            const actualFinancial = resultData.isFinanceMsg;
            const classificationCorrect = expectedFinancial === actualFinancial;
            
            const testResult = {
                success: resultData.success,
                isFinanceMsg: actualFinancial,
                expected: expectedFinancial,
                classification: classificationCorrect ? '✅ CORRECT' : '❌ WRONG',
                category: resultData.data?.category || 'N/A',
                amount: resultData.data?.amount || 0,
                loggedDate: new Date().toLocaleDateString(),
                sheet: actualFinancial ? 'Monthly Sheet' : 'Others Sheet'
            };
            
            console.log('Result:', testResult);
            
            if (classificationCorrect && resultData.success) {
                passedTests++;
            } else {
                failedTests++;
                failedCases.push({
                    testNumber: index + 1,
                    expected: testCase.expected,
                    sms: testCase.sms.substring(0, 100),
                    error: resultData.error || 'Classification mismatch'
                });
                
                if (!resultData.success) {
                    console.error('Processing failed:', resultData.error);
                }
            }
        } catch (error) {
            console.error(`Test ${index + 1} failed with error:`, error.message);
            failedTests++;
            failedCases.push({
                testNumber: index + 1,
                expected: testCase.expected,
                sms: testCase.sms.substring(0, 100),
                error: error.message
            });
        }
    });
    
    // Print summary
    console.log(`\n=== Test Results Summary ===`);
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`📊 Success Rate: ${((passedTests / (passedTests + failedTests)) * 100).toFixed(1)}%`);
    
    // Print failed cases details
    if (failedCases.length > 0) {
        console.log(`\n=== Failed Test Cases ===`);
        failedCases.forEach(failedCase => {
            console.log(`Test ${failedCase.testNumber}: ${failedCase.expected}`);
            console.log(`SMS: ${failedCase.sms}...`);
            console.log(`Error: ${failedCase.error}\n`);
        });
    }
    
    return {
        total: TEST_MESSAGES.length,
        passed: passedTests,
        failed: failedTests,
        successRate: ((passedTests / (passedTests + failedTests)) * 100).toFixed(1),
        failedCases
    };
}

/**
 * Test specific SMS classification without full processing
 */
function testSMSClassification(smsMessage) {
    console.log('=== Testing Single SMS Classification ===');
    console.log('SMS:', smsMessage);
    
    try {
        const result = doGet({ parameters: { rawMsg: [smsMessage] } });
        const resultData = JSON.parse(result.getContent());
        
        console.log('Classification Result:', {
            success: resultData.success,
            isFinanceMsg: resultData.isFinanceMsg,
            category: resultData.data?.category || 'N/A',
            amount: resultData.data?.amount || 0,
            account: resultData.data?.account || 'N/A',
            summary: resultData.data?.summary || 'N/A',
            targetSheet: resultData.isFinanceMsg ? 'Monthly Sheet' : 'Others Sheet'
        });
        
        return resultData;
    } catch (error) {
        console.error('Test failed:', error.message);
        return null;
    }
}

/**
 * Utility function to clear all test data (use with caution)
 */
function clearTestData() {
    const spreadsheet = SpreadsheetApp.openByUrl(CONFIG.SPREADSHEET_URL);
    spreadsheet.getSheets().forEach(sheet => {
        if (sheet.getName() !== 'Sheet1') {
            spreadsheet.deleteSheet(sheet);
        }
    });
    console.log('Test data cleared - all sheets except Sheet1 have been deleted');
}

/**
 * Run tests for specific categories
 */
function testByCategory(category) {
    const categoryTests = TEST_MESSAGES.filter(test => 
        test.category === category || 
        (category === 'financial' && test.expected.startsWith('Financial')) ||
        (category === 'non-financial' && test.expected.startsWith('Non-financial'))
    );
    
    console.log(`=== Testing ${category.toUpperCase()} Category ===`);
    console.log(`Found ${categoryTests.length} test cases for category: ${category}`);
    
    categoryTests.forEach((testCase, index) => {
        console.log(`\n${index + 1}. ${testCase.expected}`);
        testSMSClassification(testCase.sms);
    });
}

// Export test data and functions for potential use in other contexts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        TEST_MESSAGES,
        testScript,
        testSMSClassification,
        clearTestData,
        testByCategory
    };
}
